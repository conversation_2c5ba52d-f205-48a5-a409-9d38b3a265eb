import { ApiProperty } from '@nestjs/swagger';
import { IsString, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, IsNotEmpty } from 'class-validator';

export class CreateReviewDto {
  @ApiProperty({ description: 'Nombre del cliente que deja la reseña' })
  @IsString()
  @IsNotEmpty()
  customerName: string;

  @ApiProperty({ description: 'ID del producto reseñado' })
  @IsString()
  @IsNotEmpty()
  productId: string;

  @ApiProperty({ description: 'Nombre del producto reseñado' })
  @IsString()
  @IsNotEmpty()
  productName: string;

  @ApiProperty({ description: 'Nombre de la tienda del producto' })
  @IsString()
  @IsNotEmpty()
  storeName: string;

  @ApiProperty({ description: 'Calificación (1-5)', minimum: 1, maximum: 5 })
  @IsNumber()
  @Min(1)
  @Max(5)
  rating: number;

  @ApiProperty({ description: 'Comentario de la reseña' })
  @IsString()
  @IsNotEmpty()
  comment: string;
}

