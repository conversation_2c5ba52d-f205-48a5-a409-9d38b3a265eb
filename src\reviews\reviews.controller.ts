import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Query,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { ReviewsService } from './reviews.service';
import { CreateReviewDto, ReplyReviewDto } from './dto/reply-review.dto';
import { QueryReviewDto } from './dto/query-review.dto';

@ApiTags('reviews')
@Controller('reviews')
export class ReviewsController {
  constructor(private readonly reviewsService: ReviewsService) {}

  @Post()
  @ApiOperation({ summary: 'Crear una nueva reseña' })
  @ApiResponse({ status: 201, description: 'Reseña creada exitosamente' })
  @ApiResponse({ status: 400, description: 'Datos inválidos' })
  create(@Body() createReviewDto: CreateReviewDto) {
    return this.reviewsService.create(createReviewDto);
  }

  @Get()
  @ApiOperation({ summary: 'Listar todas las reseñas con paginación' })
  @ApiResponse({ status: 200, description: 'Lista de reseñas obtenida exitosamente' })
  @ApiQuery({ name: 'page', required: false, description: 'Número de página' })
  @ApiQuery({ name: 'limit', required: false, description: 'Elementos por página' })
  @ApiQuery({ name: 'productId', required: false, description: 'Filtrar por producto' })
  @ApiQuery({ name: 'store_id', required: false, description: 'Filtrar por tienda' })
  @ApiQuery({ name: 'user_id', required: false, description: 'Filtrar por usuario' })
  findAll(@Query() queryDto: QueryReviewDto) {
    return this.reviewsService.findAll(queryDto);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Obtener detalles de una reseña específica' })
  @ApiParam({ name: 'id', description: 'ID de la reseña' })
  @ApiResponse({ status: 200, description: 'Reseña encontrada' })
  @ApiResponse({ status: 404, description: 'Reseña no encontrada' })
  findOne(@Param('id') id: string) {
    return this.reviewsService.findOne(id);
  }

  @Patch(':id/reply')
  @ApiOperation({ summary: 'Responder a una reseña' })
  @ApiParam({ name: 'id', description: 'ID de la reseña' })
  @ApiResponse({ status: 200, description: 'Respuesta agregada exitosamente' })
  @ApiResponse({ status: 404, description: 'Reseña no encontrada' })
  @ApiResponse({ status: 400, description: 'Datos inválidos' })
  reply(@Param('id') id: string, @Body() replyReviewDto: ReplyReviewDto) {
    return this.reviewsService.reply(id, replyReviewDto);
  }

  @Get('store/:store_id')
  @ApiOperation({ summary: 'Obtener todas las reseñas de una tienda específica' })
  @ApiParam({ name: 'store_id', description: 'ID de la tienda' })
  @ApiResponse({ status: 200, description: 'Reseñas de la tienda encontradas' })
  findByStoreId(@Param('store_id') store_id: string) {
    return this.reviewsService.findByStoreId(store_id);
  }

  @Get('user/:user_id')
  @ApiOperation({ summary: 'Obtener todas las reseñas de un usuario específico' })
  @ApiParam({ name: 'user_id', description: 'ID del usuario' })
  @ApiResponse({ status: 200, description: 'Reseñas del usuario encontradas' })
  findByUserId(@Param('user_id') user_id: string) {
    return this.reviewsService.findByUserId(user_id);
  }

  @Get('average-rating/store/:store_id')
  @ApiOperation({ summary: 'Obtener calificación promedio de una tienda' })
  @ApiParam({ name: 'store_id', description: 'ID de la tienda' })
  @ApiResponse({ status: 200, description: 'Calificación promedio obtenida' })
  getAverageRatingByStoreId(@Param('store_id') store_id: string) {
    return this.reviewsService.getAverageRatingByStoreId(store_id);
  }

  @Get('average-rating/product/:productId')
  @ApiOperation({ summary: 'Obtener calificación promedio de un producto' })
  @ApiParam({ name: 'productId', description: 'ID del producto' })
  @ApiResponse({ status: 200, description: 'Calificación promedio obtenida' })
  getAverageRatingByProductId(@Param('productId') productId: string) {
    return this.reviewsService.getAverageRatingByProductId(productId);
  }
}
