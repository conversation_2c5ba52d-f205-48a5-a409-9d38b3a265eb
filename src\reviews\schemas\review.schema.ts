import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type ReviewDocument = Review & Document;

@Schema({ timestamps: true })
export class Review {
  @Prop({ required: true })
  user_id: string; // ID del usuario que dejó la reseña

  @Prop({ required: true })
  productId: string; // ID del producto reseñado

  @Prop({ required: true })
  store_id: string; // ID de la tienda que puede ver esta reseña

  @Prop({ required: true, min: 1, max: 5 })
  rating: number;

  @Prop({ required: true })
  comment: string;

  @Prop()
  reply?: string;

  @Prop({ default: Date.now })
  reviewDate: Date;

  @Prop()
  replyDate?: Date;
}

export const ReviewSchema = SchemaFactory.createForClass(Review);
