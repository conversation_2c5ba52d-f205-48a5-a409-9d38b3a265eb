import { Injectable } from '@nestjs/common';
import { ProductsService } from '../products/products.service';
import { OrdersService } from '../orders/orders.service';
import { DashboardStatsDto } from './dto/dashboard-stats.dto';

@Injectable()
export class DashboardService {
  constructor(
    private readonly productsService: ProductsService,
    private readonly ordersService: OrdersService,
  ) {}

  async getStatsForUser(user_id: string): Promise<DashboardStatsDto> {
    const [
      totalProducts,
      averageRating,
      //pendingOrders,
      //completedOrders,
      //totalRevenue,
    ] = await Promise.all([
      this.productsService.getTotalProductsByUserId(user_id).then(products => products.length),
      this.productsService.getStoreAverageRating(user_id),
      //this.ordersService.getPendingOrdersCountByUserId(user_id),
      //this.ordersService.getCompletedOrdersCountByUserId(user_id),
      //this.ordersService.getTotalRevenueByUserId(user_id),
    ]);

    return {
      totalProducts,
      averageRating,
      //pendingOrders,
      //completedOrders,
      //totalRevenue,
    };
  }
}