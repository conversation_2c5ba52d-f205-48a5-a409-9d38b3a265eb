import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Product, ProductDocument } from './schemas/product.schema';
import { CreateProductDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
import { QueryProductDto } from './dto/query-product.dto';
import { PaginationResponseDto } from '../common/dto/pagination.dto';
import { CreateProductReviewDto, ReplyProductReviewDto } from './dto/product-review.dto';

// Tipo para reseñas extendidas con información del producto
interface ExtendedReview {
  user_id: string;
  rating: number;
  comment: string;
  reply?: string;
  reviewDate: Date;
  replyDate?: Date;
  productId: any;
  productName: string;
}

@Injectable()
export class ProductsService {
  constructor(
    @InjectModel(Product.name) private productModel: Model<ProductDocument>,
  ) {}

  async create(createProductDto: CreateProductDto): Promise<Product> {
    const createdProduct = new this.productModel(createProductDto);
    return createdProduct.save();
  }

  async findAll(queryDto: QueryProductDto): Promise<{
    data: Product[];
    pagination: PaginationResponseDto;
  }> {
    const { page = 1, limit = 10, search, category, isActive } = queryDto;
    const skip = (page - 1) * limit;

    // Build filter
    const filter: any = {};
    
    if (search) {
      filter.name = { $regex: search, $options: 'i' };
    }
    
    if (category) {
      filter.category = category;
    }
    
    if (isActive !== undefined) {
      filter.isActive = isActive;
    }

    // Execute queries
    const [data, total] = await Promise.all([
      this.productModel
        .find(filter)
        .skip(skip)
        .limit(limit)
        .sort({ createdAt: -1 })
        .exec(),
      this.productModel.countDocuments(filter).exec(),
    ]);

    const totalPages = Math.ceil(total / limit);

    return {
      data,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    };
  }

  async findOne(id: string): Promise<Product> {
    const product = await this.productModel.findById(id).exec();
    if (!product) {
      throw new NotFoundException(`Product with ID ${id} not found`);
    }
    return product;
  }

  async findByUserId(user_id: string): Promise<Product[]> {
    const products = await this.productModel.find({ user_id }).exec();
    return products;
  }

  async update(id: string, updateProductDto: UpdateProductDto): Promise<Product> {
    const updatedProduct = await this.productModel
      .findByIdAndUpdate(id, updateProductDto, { new: true })
      .exec();
    
    if (!updatedProduct) {
      throw new NotFoundException(`Product with ID ${id} not found`);
    }
    
    return updatedProduct;
  }

  async remove(id: string): Promise<void> {
    const result = await this.productModel.findByIdAndDelete(id).exec();
    if (!result) {
      throw new NotFoundException(`Product with ID ${id} not found`);
    }
  }

  async getTotalProducts(): Promise<number> {
    return this.productModel.countDocuments({ isActive: true }).exec();
  }

  async getTotalProductsByUserId(user_id: string): Promise<Product[]> {
    const products = await this.productModel.find({ user_id }).exec();
    if (!products) {
      throw new NotFoundException(`No products found for user ID ${user_id}`);
    }
    return products;
  }

  // MÉTODOS PARA MANEJAR REVIEWS
  async addReview(productId: string, reviewData: CreateProductReviewDto): Promise<Product> {
    const product = await this.productModel.findById(productId).exec();
    if (!product) {
      throw new NotFoundException(`Product with ID ${productId} not found`);
    }

    const newReview = {
      ...reviewData,
      reviewDate: new Date()
    };

    product.reviews.push(newReview);
    return product.save();
  }

  async replyToReview(productId: string, reviewIndex: number, replyData: ReplyProductReviewDto): Promise<Product> {
    const product = await this.productModel.findById(productId).exec();
    if (!product) {
      throw new NotFoundException(`Product with ID ${productId} not found`);
    }

    if (reviewIndex < 0 || reviewIndex >= product.reviews.length) {
      throw new NotFoundException(`Review at index ${reviewIndex} not found`);
    }

    product.reviews[reviewIndex].reply = replyData.reply;
    product.reviews[reviewIndex].replyDate = new Date();

    return product.save();
  }

  async getProductReviews(productId: string) {
    const product = await this.productModel.findById(productId).exec();
    if (!product) {
      throw new NotFoundException(`Product with ID ${productId} not found`);
    }
    return product.reviews;
  }

  async getProductAverageRating(productId: string): Promise<number> {
    const product = await this.productModel.findById(productId).exec();
    if (!product) {
      throw new NotFoundException(`Product with ID ${productId} not found`);
    }

    if (product.reviews.length === 0) {
      return 0;
    }

    const totalRating = product.reviews.reduce((sum, review) => sum + review.rating, 0);
    return Math.round((totalRating / product.reviews.length) * 10) / 10;
  }

  async getStoreReviews(user_id: string): Promise<ExtendedReview[]> {
    const products = await this.productModel.find({ user_id }).exec();
    const allReviews: ExtendedReview[] = [];

    products.forEach(product => {
      product.reviews.forEach(review => {
        allReviews.push({
          ...review,
          productId: product._id,
          productName: product.name
        });
      });
    });

    return allReviews.sort((a, b) => new Date(b.reviewDate).getTime() - new Date(a.reviewDate).getTime());
  }

  async getUserReviews(user_id: string): Promise<ExtendedReview[]> {
    const products = await this.productModel.find({}).exec();
    const userReviews: ExtendedReview[] = [];

    products.forEach(product => {
      product.reviews.forEach(review => {
        if (review.user_id === user_id) {
          userReviews.push({
            ...review,
            productId: product._id,
            productName: product.name
          });
        }
      });
    });

    return userReviews.sort((a, b) => new Date(b.reviewDate).getTime() - new Date(a.reviewDate).getTime());
  }

  async getStoreAverageRating(user_id: string): Promise<number> {
    const products = await this.productModel.find({ user_id }).exec();
    let totalRating = 0;
    let totalReviews = 0;

    products.forEach(product => {
      product.reviews.forEach(review => {
        totalRating += review.rating;
        totalReviews++;
      });
    });

    return totalReviews > 0 ? Math.round((totalRating / totalReviews) * 10) / 10 : 0;
  }
}
