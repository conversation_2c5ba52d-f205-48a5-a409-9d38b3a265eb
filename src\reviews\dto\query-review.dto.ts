import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';
import { PaginationDto } from '../../common/dto/pagination.dto';

export class QueryReviewDto extends PaginationDto {
  @ApiPropertyOptional({ description: 'Filtrar por ID del producto' })
  @IsOptional()
  @IsString()
  productId?: string;

  @ApiPropertyOptional({ description: 'Filtrar por ID de la tienda' })
  @IsOptional()
  @IsString()
  store_id?: string;

  @ApiPropertyOptional({ description: 'Filtrar por ID del usuario que dejó la reseña' })
  @IsOptional()
  @IsString()
  user_id?: string;
}
