import { ApiProperty } from '@nestjs/swagger';
import { IsString, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, IsNotEmpty } from 'class-validator';

export class CreateReviewDto {
  @ApiProperty({ description: 'ID del usuario que deja la reseña' })
  @IsString()
  @IsNotEmpty()
  user_id: string;

  @ApiProperty({ description: 'ID del producto reseñado' })
  @IsString()
  @IsNotEmpty()
  productId: string;

  @ApiProperty({ description: 'ID de la tienda que puede ver esta reseña' })
  @IsString()
  @IsNotEmpty()
  store_id: string;

  @ApiProperty({ description: 'Calificación (1-5)', minimum: 1, maximum: 5 })
  @IsNumber()
  @Min(1)
  @Max(5)
  rating: number;

  @ApiProperty({ description: 'Comentario de la reseña' })
  @IsString()
  @IsNotEmpty()
  comment: string;
}

export class ReplyReviewDto {
  @ApiProperty({ description: 'Respuesta a la reseña' })
  @IsString()
  @IsNotEmpty()
  reply: string;
}

